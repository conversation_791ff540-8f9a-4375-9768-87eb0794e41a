using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for OptionsStrategyManager
/// Tests protective puts, covered calls, delta-efficient strategies, and options management
/// </summary>
public class OptionsStrategyManagerTests
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IVolatilityManager> _mockVolatilityManager;
    private readonly Mock<ILogger<OptionsStrategyManager>> _mockLogger;
    private readonly OptionsStrategyManager _service;

    public OptionsStrategyManagerTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockVolatilityManager = new Mock<IVolatilityManager>();
        _mockLogger = new Mock<ILogger<OptionsStrategyManager>>();
        
        _service = new OptionsStrategyManager(
            _mockMarketDataService.Object,
            _mockAlpacaFactory.Object,
            _mockVolatilityManager.Object,
            _mockLogger.Object
        );
    }

    [Fact]
    public async Task EvaluateProtectivePutAsync_WithHighVolatility_ShouldRecommendProtection()
    {
        // Arrange
        var symbol = "AAPL";
        var portfolioValue = 100000m;
        var currentPrice = 150.00m;

        SetupHighVolatilityRegime();
        SetupProtectivePutOptions(symbol, currentPrice);

        // Act
        var result = await _service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeTrue();
        result.OptionSymbol.Should().NotBeNullOrEmpty();
        result.Strike.Should().BeApproximately(147.00m, 1.0m); // Near ATM
        result.Premium.Should().BeGreaterThan(0);
        result.ProtectionLevel.Should().BeGreaterThan(0);
        result.Reason.Should().NotContain("No trigger conditions met");
    }

    [Fact]
    public async Task EvaluateProtectivePutAsync_WithLowVolatility_ShouldNotRecommendProtection()
    {
        // Arrange
        var symbol = "AAPL";
        var portfolioValue = 100000m;
        var currentPrice = 150.00m;

        SetupLowVolatilityRegime();

        // Act
        var result = await _service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No trigger conditions met");
    }

    [Fact]
    public async Task EvaluateProtectivePutAsync_WithNoSuitableOptions_ShouldReturnNoExecution()
    {
        // Arrange
        var symbol = "NOPTIONS";
        var portfolioValue = 100000m;
        var currentPrice = 150.00m;

        SetupHighVolatilityRegime();
        _mockMarketDataService
            .Setup(x => x.GetProtectivePutOptionsAsync(symbol, 30))
            .ReturnsAsync(Array.Empty<OptionData>());

        // Act
        var result = await _service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No suitable ATM put options found");
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithGoodYield_ShouldRecommendExecution()
    {
        // Arrange
        var symbol = "MSFT";
        var sharesOwned = 1000m;
        var currentPrice = 200.00m;

        SetupCoveredCallOptions(symbol, currentPrice);

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeTrue();
        result.OptionSymbol.Should().NotBeNullOrEmpty();
        result.Strike.Should().BeGreaterThan(currentPrice);
        result.Premium.Should().BeGreaterThan(0);
        result.AnnualizedYield.Should().BeGreaterThan(0.15m); // Target 15%+ yield
        result.AssignmentRisk.Should().BeLessThan(0.25m); // Low assignment risk
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithPoorYield_ShouldNotRecommendExecution()
    {
        // Arrange
        var symbol = "LOWVOL";
        var sharesOwned = 1000m;
        var currentPrice = 100.00m;

        SetupLowYieldCoveredCallOptions(symbol, currentPrice);

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("yield or assignment risk criteria not met");
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithNoSuitableOptions_ShouldReturnNoExecution()
    {
        // Arrange
        var symbol = "NOCALLS";
        var sharesOwned = 1000m;
        var currentPrice = 100.00m;

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(Array.Empty<OptionData>());

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No suitable call options found");
    }

    [Fact]
    public async Task EvaluateDeltaEfficientExposureAsync_WithGoodCapitalEfficiency_ShouldRecommendStrategy()
    {
        // Arrange
        var symbol = "TSLA";
        var targetExposure = 50000m;
        var currentPrice = 300.00m;

        SetupDeltaEfficientOptions(symbol, currentPrice);

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeTrue();
        result.LongCallSymbol.Should().NotBeNullOrEmpty();
        result.ShortCallSymbol.Should().NotBeNullOrEmpty();
        result.LongStrike.Should().BeLessThan(currentPrice);
        result.ShortStrike.Should().BeGreaterThan(currentPrice);
        result.CapitalEfficiency.Should().BeGreaterThan(2.5m);
        result.EffectiveDelta.Should().BeGreaterThan(0.60m);
    }

    [Fact]
    public async Task EvaluateDeltaEfficientExposureAsync_WithPoorEfficiency_ShouldNotRecommendStrategy()
    {
        // Arrange
        var symbol = "INEFFICIENT";
        var targetExposure = 50000m;
        var currentPrice = 100.00m;

        SetupInefficientDeltaOptions(symbol, currentPrice);

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("efficiency or delta criteria not met");
    }

    [Fact]
    public async Task EvaluateDeltaEfficientExposureAsync_WithInsufficientOptionsChain_ShouldReturnNoExecution()
    {
        // Arrange
        var symbol = "NOOPTIONS";
        var targetExposure = 50000m;
        var currentPrice = 100.00m;

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(Array.Empty<OptionData>());

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("Insufficient options chain for delta-efficient strategy");
    }

    [Fact]
    public async Task ManageExistingOptionsAsync_WithOptionsPositions_ShouldProcessAllPositions()
    {
        // Arrange
        var positions = new[]
        {
            CreateMockPosition("AAPL240119C00150000", 5, 1000m),
            CreateMockPosition("MSFT240119P00200000", -10, -500m),
            CreateMockPosition("TSLA", 100, 5000m) // Stock position (should be ignored)
        };

        _mockMarketDataService
            .Setup(x => x.GetPositionsAsync())
            .ReturnsAsync(positions);

        // Act
        await _service.ManageExistingOptionsAsync();

        // Assert - Should complete without throwing
        _mockMarketDataService.Verify(x => x.GetPositionsAsync(), Times.Once);
    }

    [Fact]
    public async Task ManageExistingOptionsAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        _mockMarketDataService
            .Setup(x => x.GetPositionsAsync())
            .ThrowsAsync(new Exception("Positions data unavailable"));

        // Act & Assert - Should not throw
        await _service.ManageExistingOptionsAsync();

        // Verify error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error managing existing options positions")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ManageExpirationRiskAsync_WithExpiringPositions_ShouldHandleExpiration()
    {
        // Arrange
        var positions = new[]
        {
            CreateMockPosition("AAPL240119C00150000", 5, 1000m),
            CreateMockPosition("MSFT240126P00200000", -10, -500m)
        };

        _mockMarketDataService
            .Setup(x => x.GetPositionsAsync())
            .ReturnsAsync(positions);

        // Act
        await _service.ManageExpirationRiskAsync();

        // Assert - Should complete without throwing
        _mockMarketDataService.Verify(x => x.GetPositionsAsync(), Times.Once);
    }

    [Fact]
    public async Task ManageExpirationRiskAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        _mockMarketDataService
            .Setup(x => x.GetPositionsAsync())
            .ThrowsAsync(new Exception("Expiration risk check failed"));

        // Act & Assert - Should not throw
        await _service.ManageExpirationRiskAsync();

        // Verify error was logged
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error managing expiration risk")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    private void SetupHighVolatilityRegime()
    {
        var regime = new VolatilityRegime(
            CurrentVix: 28.0m,
            VixSma30: 22.0m,
            IsHighVol: true,
            IsVixSpike: false,
            PositionSizeMultiplier: 0.7m,
            RegimeName: "High Volatility"
        );

        _mockVolatilityManager
            .Setup(x => x.GetCurrentRegimeAsync())
            .ReturnsAsync(regime);
    }

    private void SetupLowVolatilityRegime()
    {
        var regime = new VolatilityRegime(
            CurrentVix: 12.0m,
            VixSma30: 15.0m,
            IsHighVol: false,
            IsVixSpike: false,
            PositionSizeMultiplier: 1.2m,
            RegimeName: "Low Volatility"
        );

        _mockVolatilityManager
            .Setup(x => x.GetCurrentRegimeAsync())
            .ReturnsAsync(regime);
    }

    private void SetupProtectivePutOptions(string symbol, decimal currentPrice)
    {
        var putOptions = new[]
        {
            new OptionData(
                Symbol: $"{symbol}240119P00147000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(30),
                Strike: 147.00m,
                OptionType: "put",
                LastPrice: 2.90m,
                Bid: 2.80m,
                Ask: 3.00m,
                Volume: 1000,
                OpenInterest: 5000,
                ImpliedVolatility: 0.25m,
                Delta: -0.35m,
                Gamma: 0.02m,
                Theta: -0.05m,
                Vega: 0.15m
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetProtectivePutOptionsAsync(symbol, 30))
            .ReturnsAsync(putOptions);
    }

    private void SetupCoveredCallOptions(string symbol, decimal currentPrice)
    {
        var callOptions = new[]
        {
            new OptionData(
                Symbol: $"{symbol}240119C00210000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(30),
                Strike: 210.00m,
                OptionType: "call",
                LastPrice: 3.60m,
                Bid: 3.50m,
                Ask: 3.70m,
                Volume: 800,
                OpenInterest: 3000,
                ImpliedVolatility: 0.22m,
                Delta: 0.20m,
                Gamma: 0.015m,
                Theta: -0.04m,
                Vega: 0.12m
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(callOptions);
    }

    private void SetupLowYieldCoveredCallOptions(string symbol, decimal currentPrice)
    {
        var callOptions = new[]
        {
            new OptionData(
                Symbol: $"{symbol}240119C00105000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(30),
                Strike: 105.00m,
                OptionType: "call",
                LastPrice: 0.55m, // Low premium
                Bid: 0.50m,
                Ask: 0.60m,
                Volume: 100,
                OpenInterest: 500,
                ImpliedVolatility: 0.15m,
                Delta: 0.15m,
                Gamma: 0.01m,
                Theta: -0.02m,
                Vega: 0.08m
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(callOptions);
    }

    private void SetupDeltaEfficientOptions(string symbol, decimal currentPrice)
    {
        var options = new[]
        {
            // Deep ITM call
            new OptionData(
                Symbol: $"{symbol}240119C00250000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 250.00m, // Deep ITM
                OptionType: "call",
                LastPrice: 49.00m,
                Bid: 48.00m,
                Ask: 50.00m,
                Volume: 200,
                OpenInterest: 1000,
                ImpliedVolatility: 0.20m,
                Delta: 0.85m,
                Gamma: 0.005m,
                Theta: -0.08m,
                Vega: 0.20m
            ),
            // OTM call for financing
            new OptionData(
                Symbol: $"{symbol}240119C00330000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 330.00m, // OTM
                OptionType: "call",
                LastPrice: 8.25m,
                Bid: 8.00m,
                Ask: 8.50m,
                Volume: 500,
                OpenInterest: 2000,
                ImpliedVolatility: 0.25m,
                Delta: 0.15m,
                Gamma: 0.01m,
                Theta: -0.03m,
                Vega: 0.10m
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(options);
    }

    private void SetupInefficientDeltaOptions(string symbol, decimal currentPrice)
    {
        var options = new[]
        {
            // Expensive deep ITM call
            new OptionData(
                Symbol: $"{symbol}240119C00080000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 80.00m,
                OptionType: "call",
                LastPrice: 25.50m, // Expensive
                Bid: 25.00m,
                Ask: 26.00m,
                Volume: 50,
                OpenInterest: 200,
                ImpliedVolatility: 0.30m,
                Delta: 0.80m,
                Gamma: 0.01m,
                Theta: -0.05m,
                Vega: 0.15m
            ),
            // Low premium OTM call
            new OptionData(
                Symbol: $"{symbol}240119C00120000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 120.00m,
                OptionType: "call",
                LastPrice: 0.55m, // Low premium
                Bid: 0.50m,
                Ask: 0.60m,
                Volume: 100,
                OpenInterest: 300,
                ImpliedVolatility: 0.35m,
                Delta: 0.10m,
                Gamma: 0.005m,
                Theta: -0.02m,
                Vega: 0.05m
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(options);
    }

    [Theory]
    [InlineData(50000, 150.00, true)] // High portfolio value
    [InlineData(10000, 150.00, false)] // Low portfolio value
    [InlineData(100000, 50.00, false)] // Low stock price
    public async Task EvaluateProtectivePutAsync_WithVariousPortfolioSizes_ShouldHandleCorrectly(
        decimal portfolioValue, decimal currentPrice, bool shouldHaveOptions)
    {
        // Arrange
        var symbol = "PORTFOLIO_TEST";
        SetupHighVolatilityRegime();

        if (shouldHaveOptions)
        {
            SetupProtectivePutOptions(symbol, currentPrice);
        }
        else
        {
            _mockMarketDataService
                .Setup(x => x.GetProtectivePutOptionsAsync(symbol, 30))
                .ReturnsAsync(Array.Empty<OptionData>());
        }

        // Act
        var result = await _service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice);

        // Assert
        if (shouldHaveOptions)
        {
            result.ShouldExecute.Should().BeTrue();
        }
        else
        {
            result.ShouldExecute.Should().BeFalse();
        }
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithZeroShares_ShouldReturnNoExecution()
    {
        // Arrange
        var symbol = "NOSHARES";
        var sharesOwned = 0m;
        var currentPrice = 100.00m;

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No suitable call options found");
    }

    [Fact]
    public async Task EvaluateDeltaEfficientExposureAsync_WithZeroTargetExposure_ShouldReturnNoExecution()
    {
        // Arrange
        var symbol = "ZEROEXPOSURE";
        var targetExposure = 0m;
        var currentPrice = 100.00m;

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
    }

    [Fact]
    public async Task EvaluateProtectivePutAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "CONCURRENT";
        var portfolioValue = 100000m;
        var currentPrice = 150.00m;

        SetupHighVolatilityRegime();
        SetupProtectivePutOptions(symbol, currentPrice);

        // Act - Simulate concurrent calls
        var tasks = new List<Task<ProtectivePutResult>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(5);
        results.Should().OnlyContain(r => r.ShouldExecute);
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "CONCURRENT_CC";
        var sharesOwned = 1000m;
        var currentPrice = 200.00m;

        SetupCoveredCallOptions(symbol, currentPrice);

        // Act - Simulate concurrent calls
        var tasks = new List<Task<CoveredCallResult>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(5);
        results.Should().OnlyContain(r => r.ShouldExecute);
    }

    [Fact]
    public async Task EvaluateProtectivePutAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "ERROR_PUT";
        var portfolioValue = 100000m;
        var currentPrice = 150.00m;

        _mockVolatilityManager
            .Setup(x => x.GetCurrentRegimeAsync())
            .ThrowsAsync(new Exception("Volatility analysis failed"));

        // Act
        var result = await _service.EvaluateProtectivePutAsync(symbol, portfolioValue, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No trigger conditions met");
    }

    [Fact]
    public async Task EvaluateCoveredCallAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "ERROR_CC";
        var sharesOwned = 1000m;
        var currentPrice = 200.00m;

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ThrowsAsync(new Exception("Options data unavailable"));

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("No suitable call options found");
    }

    [Fact]
    public async Task EvaluateDeltaEfficientExposureAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "ERROR_DELTA";
        var targetExposure = 50000m;
        var currentPrice = 300.00m;

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ThrowsAsync(new Exception("Options chain unavailable"));

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().BeFalse();
        result.Reason.Should().Contain("Insufficient options chain for delta-efficient strategy");
    }

    [Theory]
    [InlineData(0.10, 0.05, false)] // Low yield, low assignment risk
    [InlineData(0.20, 0.30, false)] // Good yield, high assignment risk
    [InlineData(0.18, 0.20, true)]  // Good yield, acceptable assignment risk
    public async Task EvaluateCoveredCallAsync_WithVariousYieldAndRisk_ShouldEvaluateCorrectly(
        decimal annualizedYield, decimal assignmentRisk, bool shouldExecute)
    {
        // Arrange
        var symbol = "YIELD_TEST";
        var sharesOwned = 1000m;
        var currentPrice = 100.00m;

        // Calculate premium to achieve target yield
        var daysToExpiration = 30;
        var targetPremium = (annualizedYield * sharesOwned * currentPrice * daysToExpiration) / 365m / 100m;

        var callOptions = new[]
        {
            new OptionQuote(
                Symbol: $"{symbol}240119C00105000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(daysToExpiration),
                Strike: 105.00m,
                OptionType: "call",
                Bid: targetPremium, Ask: targetPremium + 0.10m, LastPrice: targetPremium + 0.05m,
                Volume: 500, OpenInterest: 2000,
                ImpliedVolatility: 0.20m,
                Delta: assignmentRisk, Gamma: 0.015m, Theta: -0.04m, Vega: 0.12m,
                Timestamp: DateTime.UtcNow
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(callOptions);

        // Act
        var result = await _service.EvaluateCoveredCallAsync(symbol, sharesOwned, currentPrice);

        // Assert
        result.ShouldExecute.Should().Be(shouldExecute);
        if (shouldExecute)
        {
            result.AnnualizedYield.Should().BeGreaterThan(0.15m);
            result.AssignmentRisk.Should().BeLessThan(0.25m);
        }
    }

    [Theory]
    [InlineData(1.5, 0.50, false)] // Low efficiency, low delta
    [InlineData(3.0, 0.70, true)]  // Good efficiency, good delta
    [InlineData(4.0, 0.40, false)] // Good efficiency, low delta
    public async Task EvaluateDeltaEfficientExposureAsync_WithVariousEfficiency_ShouldEvaluateCorrectly(
        decimal capitalEfficiency, decimal effectiveDelta, bool shouldExecute)
    {
        // Arrange
        var symbol = "EFFICIENCY_TEST";
        var targetExposure = 50000m;
        var currentPrice = 100.00m;

        // Calculate premiums to achieve target efficiency
        var longDelta = effectiveDelta + 0.15m;
        var shortDelta = 0.15m;
        var sharesEquivalent = effectiveDelta * 100m;
        var netPremium = (sharesEquivalent * currentPrice) / capitalEfficiency;
        var longCallCost = netPremium + 5.00m;
        var shortCallCredit = 5.00m;

        var options = new[]
        {
            new OptionQuote(
                Symbol: $"{symbol}240119C00085000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 85.00m,
                OptionType: "call",
                Bid: longCallCost - 0.50m, Ask: longCallCost, LastPrice: longCallCost - 0.25m,
                Volume: 200, OpenInterest: 1000,
                ImpliedVolatility: 0.20m,
                Delta: longDelta, Gamma: 0.005m, Theta: -0.08m, Vega: 0.20m,
                Timestamp: DateTime.UtcNow
            ),
            new OptionQuote(
                Symbol: $"{symbol}240119C00115000",
                UnderlyingSymbol: symbol,
                ExpirationDate: DateTime.UtcNow.AddDays(60),
                Strike: 115.00m,
                OptionType: "call",
                Bid: shortCallCredit, Ask: shortCallCredit + 0.25m, LastPrice: shortCallCredit + 0.10m,
                Volume: 500, OpenInterest: 2000,
                ImpliedVolatility: 0.25m,
                Delta: shortDelta, Gamma: 0.01m, Theta: -0.03m, Vega: 0.10m,
                Timestamp: DateTime.UtcNow
            )
        };

        _mockMarketDataService
            .Setup(x => x.GetOptionsDataAsync(symbol, It.IsAny<DateTime>()))
            .ReturnsAsync(options);

        // Act
        var result = await _service.EvaluateDeltaEfficientExposureAsync(symbol, targetExposure, currentPrice);

        // Assert
        result.ShouldExecute.Should().Be(shouldExecute);
        if (shouldExecute)
        {
            result.CapitalEfficiency.Should().BeGreaterThan(2.5m);
            result.EffectiveDelta.Should().BeGreaterThan(0.60m);
        }
    }

    private static IPosition CreateMockPosition(string symbol, decimal quantity, decimal unrealizedPnL)
    {
        var position = new Mock<IPosition>();
        position.Setup(p => p.Symbol).Returns(symbol);
        position.Setup(p => p.Quantity).Returns(quantity);
        position.Setup(p => p.UnrealizedProfitLoss).Returns(unrealizedPnL);
        return position.Object;
    }
}
